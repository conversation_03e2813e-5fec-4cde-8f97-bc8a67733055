//@version=5
indicator("Professional Trading Signal Indicator", shorttitle="PTS", overlay=false, max_bars_back=1000)

// ============================================================================
// INPUT PARAMETERS
// ============================================================================

// Teknik İndikatör Ayarları
g1 = "📊 Teknik İndikatör Ayarları"
rsi_length = input.int(14, "RSI Periyodu", minval=1, group=g1)
rsi_oversold = input.int(30, "RSI Oversold Seviyesi", minval=1, maxval=50, group=g1)
rsi_overbought = input.int(70, "RSI Overbought Seviyesi", minval=50, maxval=99, group=g1)

macd_fast = input.int(12, "MACD Fast Length", minval=1, group=g1)
macd_slow = input.int(26, "MACD Slow Length", minval=1, group=g1)
macd_signal = input.int(9, "MACD Signal Length", minval=1, group=g1)

ema_fast = input.int(21, "EMA Fast Periyodu", minval=1, group=g1)
ema_slow = input.int(50, "EMA Slow Periyodu", minval=1, group=g1)

bb_length = input.int(20, "Bollinger Bands Periyodu", minval=1, group=g1)
bb_mult = input.float(2.0, "Bollinger Bands Multiplier", minval=0.1, step=0.1, group=g1)

volume_length = input.int(20, "Volume SMA Periyodu", minval=1, group=g1)
atr_length = input.int(14, "ATR Periyodu", minval=1, group=g1)

// Filtreleme Ayarları
g2 = "🔍 Filtreleme Ayarları"
use_trend_filter = input.bool(true, "Trend Filtresi Aktif", group=g2)
use_volatility_filter = input.bool(true, "Volatilite Filtresi Aktif", group=g2)
use_volume_filter = input.bool(true, "Volume Filtresi Aktif", group=g2)
use_time_filter = input.bool(false, "Zaman Filtresi Aktif", group=g2)

volatility_threshold = input.float(1.5, "Volatilite Eşiği (ATR Multiplier)", minval=0.1, step=0.1, group=g2)
volume_threshold = input.float(1.2, "Volume Eşiği (SMA Multiplier)", minval=0.1, step=0.1, group=g2)

start_hour = input.int(9, "İşlem Başlangıç Saati", minval=0, maxval=23, group=g2)
end_hour = input.int(17, "İşlem Bitiş Saati", minval=0, maxval=23, group=g2)

// Risk Yönetimi
g3 = "⚠️ Risk Yönetimi"
stop_loss_pct = input.float(2.0, "Stop Loss (%)", minval=0.1, step=0.1, group=g3)
take_profit_pct = input.float(4.0, "Take Profit (%)", minval=0.1, step=0.1, group=g3)

// Backtest Ayarları
g4 = "📈 Backtest Ayarları"
initial_capital = input.float(1000, "Başlangıç Sermayesi (USD)", minval=100, group=g4)
commission_pct = input.float(0.1, "Komisyon (%)", minval=0, step=0.01, group=g4)
position_size_pct = input.float(100, "Position Size (%)", minval=1, maxval=100, group=g4)

// Webhook Ayarları
g5 = "🔗 Webhook Ayarları"
// Webhook mesajları sabit string olarak tanımlanmalı
webhook_buy_msg = '{"action": "buy", "symbol": "{{ticker}}", "price": "{{close}}", "timestamp": "{{time}}"}'
webhook_sell_msg = '{"action": "sell", "symbol": "{{ticker}}", "price": "{{close}}", "timestamp": "{{time}}"}'

// Dashboard Ayarları
g6 = "📊 Dashboard Ayarları"
show_dashboard = input.bool(true, "Dashboard Göster", group=g6)
dashboard_position = input.string("top_right", "Dashboard Pozisyonu", 
     options=["top_left", "top_right", "bottom_left", "bottom_right"], group=g6)

// ============================================================================
// TEKNIK İNDİKATÖR HESAPLAMALARI
// ============================================================================

// RSI
rsi = ta.rsi(close, rsi_length)

// MACD
[macd_line, signal_line, macd_histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// EMA
ema_21 = ta.ema(close, ema_fast)
ema_50 = ta.ema(close, ema_slow)

// Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)

// Volume
volume_sma = ta.sma(volume, volume_length)

// ATR
atr = ta.atr(atr_length)

// ============================================================================
// FİLTRELEME SİSTEMİ
// ============================================================================

// Trend Filtresi
trend_up = ema_21 > ema_50
trend_down = ema_21 < ema_50
trend_filter_long = use_trend_filter ? trend_up : true
trend_filter_short = use_trend_filter ? trend_down : true

// Volatilite Filtresi
volatility_ok = atr > ta.sma(atr, 20) * volatility_threshold
volatility_filter = use_volatility_filter ? volatility_ok : true

// Volume Filtresi
volume_ok = volume > volume_sma * volume_threshold
volume_filter = use_volume_filter ? volume_ok : true

// Zaman Filtresi
current_hour = hour(time)
time_ok = start_hour <= end_hour ? 
     (current_hour >= start_hour and current_hour <= end_hour) : 
     (current_hour >= start_hour or current_hour <= end_hour)
time_filter = use_time_filter ? time_ok : true

// ============================================================================
// SİNYAL KOŞULLARI
// ============================================================================

// Temel sinyal koşulları
rsi_buy_condition = rsi > rsi_oversold and rsi[1] <= rsi_oversold
rsi_sell_condition = rsi < rsi_overbought and rsi[1] >= rsi_overbought

macd_buy_condition = macd_line > signal_line and macd_line[1] <= signal_line[1]
macd_sell_condition = macd_line < signal_line and macd_line[1] >= signal_line[1]

price_buy_condition = close > ema_21
price_sell_condition = close < ema_21

// Ana sinyal koşulları
buy_signal_base = rsi_buy_condition and macd_buy_condition and price_buy_condition
sell_signal_base = rsi_sell_condition and macd_sell_condition and price_sell_condition

// Filtreli sinyaller
buy_signal = buy_signal_base and trend_filter_long and volatility_filter and volume_filter and time_filter
sell_signal = sell_signal_base and trend_filter_short and volatility_filter and volume_filter and time_filter

// ============================================================================
// BACKTEST SİSTEMİ
// ============================================================================

var float entry_price = na
var bool in_long_position = false
var bool in_short_position = false
var int total_trades = 0
var int winning_trades = 0
var float total_pnl = 0.0
var float max_drawdown = 0.0
var float peak_equity = 0.0
var array<float> trade_returns = array.new<float>()

// Position yönetimi
if buy_signal and not in_long_position and not in_short_position
    entry_price := close
    in_long_position := true
    in_short_position := false

if sell_signal and not in_short_position and not in_long_position
    entry_price := close
    in_short_position := true
    in_long_position := false

// Exit koşulları
long_stop_loss = entry_price * (1 - stop_loss_pct / 100)
long_take_profit = entry_price * (1 + take_profit_pct / 100)
short_stop_loss = entry_price * (1 + stop_loss_pct / 100)
short_take_profit = entry_price * (1 - take_profit_pct / 100)

long_exit = in_long_position and (close <= long_stop_loss or close >= long_take_profit or sell_signal)
short_exit = in_short_position and (close >= short_stop_loss or close <= short_take_profit or buy_signal)

// P&L hesaplama
if long_exit and in_long_position
    trade_return = (close - entry_price) / entry_price * 100
    total_trades += 1
    if trade_return > 0
        winning_trades += 1
    total_pnl += trade_return
    array.push(trade_returns, trade_return)
    in_long_position := false

if short_exit and in_short_position
    trade_return = (entry_price - close) / entry_price * 100
    total_trades += 1
    if trade_return > 0
        winning_trades += 1
    total_pnl += trade_return
    array.push(trade_returns, trade_return)
    in_short_position := false

// Equity curve ve drawdown hesaplama
current_equity = initial_capital * (1 + total_pnl / 100)
peak_equity := math.max(peak_equity, current_equity)
current_drawdown = peak_equity > 0 ? (peak_equity - current_equity) / peak_equity * 100 : 0
max_drawdown := math.max(max_drawdown, current_drawdown)

// İstatistik hesaplamaları
win_rate = total_trades > 0 ? winning_trades / total_trades * 100 : 0
profit_factor = 0.0
avg_win = 0.0
avg_loss = 0.0

if array.size(trade_returns) > 0
    total_wins = 0.0
    total_losses = 0.0
    win_count = 0
    loss_count = 0
    
    for i = 0 to array.size(trade_returns) - 1
        ret = array.get(trade_returns, i)
        if ret > 0
            total_wins += ret
            win_count += 1
        else
            total_losses += math.abs(ret)
            loss_count += 1
    
    avg_win := win_count > 0 ? total_wins / win_count : 0
    avg_loss := loss_count > 0 ? total_losses / loss_count : 0
    profit_factor := avg_loss > 0 ? avg_win / avg_loss : 0

// ============================================================================
// GÖRSELLEŞTİRME
// ============================================================================

// Ana indikatör çizgileri
plot(rsi, "RSI", color=color.blue, linewidth=2)
hline(rsi_overbought, "Overbought", color=color.red, linestyle=hline.style_dashed)
hline(rsi_oversold, "Oversold", color=color.green, linestyle=hline.style_dashed)
hline(50, "Midline", color=color.gray, linestyle=hline.style_dotted)

// Sinyaller
plotshape(buy_signal, title="Buy Signal", location=location.bottom, color=color.green, 
     style=shape.triangleup, size=size.normal, text="BUY")
plotshape(sell_signal, title="Sell Signal", location=location.top, color=color.red, 
     style=shape.triangledown, size=size.normal, text="SELL")

// ============================================================================
// DASHBOARD
// ============================================================================

if show_dashboard and barstate.islast
    var table dashboard = table.new(
         position = dashboard_position == "top_left" ? position.top_left :
                   dashboard_position == "top_right" ? position.top_right :
                   dashboard_position == "bottom_left" ? position.bottom_left : position.bottom_right,
         columns = 2, rows = 10, bgcolor = color.white, border_width = 1)
    
    table.cell(dashboard, 0, 0, "📊 TRADING STATISTICS", text_color=color.white, bgcolor=color.blue, text_size=size.normal)
    table.cell(dashboard, 1, 0, "", text_color=color.white, bgcolor=color.blue)
    
    table.cell(dashboard, 0, 1, "Total Trades", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 1, str.tostring(total_trades), text_color=color.black, text_size=size.small)
    
    table.cell(dashboard, 0, 2, "Win Rate (%)", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 2, str.tostring(win_rate, "#.##"), 
         text_color = win_rate >= 50 ? color.green : color.red, text_size=size.small)
    
    table.cell(dashboard, 0, 3, "Profit Factor", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 3, str.tostring(profit_factor, "#.##"), 
         text_color = profit_factor >= 1.5 ? color.green : color.red, text_size=size.small)
    
    table.cell(dashboard, 0, 4, "Total P&L (%)", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 4, str.tostring(total_pnl, "#.##"), 
         text_color = total_pnl > 0 ? color.green : color.red, text_size=size.small)
    
    table.cell(dashboard, 0, 5, "Max Drawdown (%)", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 5, str.tostring(max_drawdown, "#.##"), 
         text_color = max_drawdown <= 20 ? color.green : color.red, text_size=size.small)
    
    table.cell(dashboard, 0, 6, "Avg Win (%)", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 6, str.tostring(avg_win, "#.##"), text_color=color.green, text_size=size.small)
    
    table.cell(dashboard, 0, 7, "Avg Loss (%)", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 7, str.tostring(avg_loss, "#.##"), text_color=color.red, text_size=size.small)
    
    table.cell(dashboard, 0, 8, "Current Equity", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 8, "$" + str.tostring(current_equity, "#.##"), text_color=color.black, text_size=size.small)
    
    position_text = in_long_position ? "LONG" : in_short_position ? "SHORT" : "FLAT"
    position_color = in_long_position ? color.green : in_short_position ? color.red : color.gray
    table.cell(dashboard, 0, 9, "Position", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 9, position_text, text_color=position_color, text_size=size.small)

// ============================================================================
// ALERT KOŞULLARI
// ============================================================================

alertcondition(buy_signal, title="Buy Signal Alert", message=webhook_buy_msg)
alertcondition(sell_signal, title="Sell Signal Alert", message=webhook_sell_msg)
alertcondition(long_exit, title="Long Exit Alert", message="Long position closed")
alertcondition(short_exit, title="Short Exit Alert", message="Short position closed")
