# Parametre Optimizasyon Rehberi

## 🎯 Optimizasyon Stratejisi

### 1. Temel Yaklaşım
- **Aşamalı Optimizasyon:** Bir seferde tek parametre grubu optimize edin
- **Backtest Periyodu:** En az 3 aylık veri kullanın
- **Out-of-Sample Test:** Optimizasyon sonrası farklı periyotta test edin
- **Walk-Forward Analysis:** Dönemsel olarak parametreleri güncelleyin

### 2. Optimizasyon Sırası
1. **Temel İndikatörler** (RSI, MACD, EMA)
2. **Filtreleme Parametreleri**
3. **Risk Yönetimi** (Stop Loss, Take Profit)
4. **Position Sizing**

## 📊 Piyasa Tipine Göre Optimizasyon

### 🚀 Trending Piyasalar
```
RSI: 25/75 (daha geniş aralık)
MACD: 8,21,5 (da<PERSON> h<PERSON>l<PERSON>)
EMA: 13,34 (<PERSON><PERSON><PERSON><PERSON>)
Stop Loss: %1.5
Take Profit: %4.5
Trend Filtresi: MUTLAKA AKTİF
Volume Filtresi: Aktif
```

### 📈 Sideways Piyasalar
```
RSI: 20/80 (daha dar aralık)
MACD: 12,26,9 (standart)
EMA: 21,50 (standart)
Stop Loss: %2.5
Take Profit: %3.0
Volatilite Filtresi: MUTLAKA AKTİF
Zaman Filtresi: Aktif (volatil saatler)
```

### 📉 Volatil Piyasalar
```
RSI: 30/70 (standart)
MACD: 16,35,12 (daha yavaş)
EMA: 34,89 (daha uzun)
Stop Loss: %3.0
Take Profit: %6.0
Volatilite Filtresi: Aktif (eşik 2.0)
Volume Filtresi: Aktif (eşik 1.5)
```

## 🕐 Timeframe Bazlı Optimizasyon

### ⚡ 5 Dakika
```
RSI: 21 periyot, 20/80 seviyeler
MACD: 8,17,5
EMA: 13,21
Volume: 10 periyot
ATR: 10 periyot
Stop Loss: %1.0
Take Profit: %2.0
```

### 🎯 15 Dakika (Varsayılan)
```
RSI: 14 periyot, 30/70 seviyeler
MACD: 12,26,9
EMA: 21,50
Volume: 20 periyot
ATR: 14 periyot
Stop Loss: %2.0
Take Profit: %4.0
```

### ⏰ 1 Saat
```
RSI: 14 periyot, 35/65 seviyeler
MACD: 12,26,9
EMA: 50,200
Volume: 50 periyot
ATR: 20 periyot
Stop Loss: %3.0
Take Profit: %6.0
```

### 📅 4 Saat
```
RSI: 14 periyot, 40/60 seviyeler
MACD: 12,26,9
EMA: 89,200
Volume: 100 periyot
ATR: 30 periyot
Stop Loss: %5.0
Take Profit: %10.0
```

## 💰 Enstrüman Bazlı Optimizasyon

### 🪙 Kripto Paralar
```
RSI: 25/75 (yüksek volatilite)
MACD: 8,21,5 (hızlı sinyaller)
EMA: 13,34
Volatilite Eşiği: 2.5
Volume Eşiği: 2.0
Stop Loss: %3.0
Take Profit: %6.0
Zaman Filtresi: 24/7 (kapalı)
```

### 💱 Forex (Major Pairs)
```
RSI: 30/70 (standart)
MACD: 12,26,9
EMA: 21,50
Volatilite Eşiği: 1.2
Volume Eşiği: 1.1
Stop Loss: %1.5
Take Profit: %3.0
Zaman Filtresi: London/NY session
```

### 📈 Hisse Senetleri
```
RSI: 35/65 (daha konservatif)
MACD: 12,26,9
EMA: 21,50
Volatilite Eşiği: 1.0
Volume Eşiği: 1.5 (önemli)
Stop Loss: %2.0
Take Profit: %4.0
Zaman Filtresi: 09:30-16:00
```

### 🏭 Emtialar
```
RSI: 30/70
MACD: 16,35,12 (daha yavaş)
EMA: 34,89
Volatilite Eşiği: 1.8
Volume Eşiği: 1.3
Stop Loss: %2.5
Take Profit: %5.0
```

## 🔧 Optimizasyon Metodolojisi

### 1. Grid Search Yöntemi
```
RSI Periyodu: [10, 12, 14, 16, 18, 20]
RSI Oversold: [20, 25, 30, 35]
RSI Overbought: [65, 70, 75, 80]
MACD Fast: [8, 10, 12, 14]
MACD Slow: [21, 24, 26, 28]
```

### 2. Genetic Algorithm
- Popülasyon: 50 parametre seti
- Nesil: 100 iterasyon
- Mutasyon oranı: %5
- Crossover oranı: %80

### 3. Bayesian Optimization
- Acquisition function: Expected Improvement
- Kernel: Gaussian Process
- Iteration: 200

## 📈 Performans Metrikleri

### Birincil Metrikler
1. **Sharpe Ratio** (hedef: >1.5)
2. **Profit Factor** (hedef: >1.5)
3. **Win Rate** (hedef: >50%)
4. **Max Drawdown** (hedef: <20%)

### İkincil Metrikler
1. **Calmar Ratio** (Annual Return / Max Drawdown)
2. **Sortino Ratio** (Downside deviation bazlı)
3. **Recovery Factor** (Net Profit / Max Drawdown)
4. **Expectancy** (Avg Win * Win Rate - Avg Loss * Loss Rate)

## 🎛️ Adaptif Parametre Sistemi

### Volatilite Bazlı Adaptasyon
```pine
// Yüksek volatilite dönemlerinde
if atr > ta.sma(atr, 50) * 2
    rsi_oversold := 25
    rsi_overbought := 75
    stop_loss_pct := 3.0
else
    rsi_oversold := 30
    rsi_overbought := 70
    stop_loss_pct := 2.0
```

### Trend Gücü Bazlı Adaptasyon
```pine
// Güçlü trend dönemlerinde
trend_strength = math.abs(ema_21 - ema_50) / ema_50 * 100
if trend_strength > 2.0
    macd_fast := 8
    macd_slow := 21
else
    macd_fast := 12
    macd_slow := 26
```

## 📊 Backtest Validasyonu

### 1. In-Sample vs Out-of-Sample
- **In-Sample:** %70 (optimizasyon için)
- **Out-of-Sample:** %30 (validasyon için)
- **Minimum Performans Farkı:** <%20

### 2. Walk-Forward Analysis
- **Optimizasyon Periyodu:** 3 ay
- **Test Periyodu:** 1 ay
- **Adım:** 1 ay
- **Minimum Test Sayısı:** 12

### 3. Monte Carlo Simulasyonu
- **Simulasyon Sayısı:** 1000
- **Confidence Level:** %95
- **Risk of Ruin:** <%5

## ⚠️ Optimizasyon Tuzakları

### 1. Over-Fitting
- **Belirti:** In-sample çok iyi, out-of-sample kötü
- **Çözüm:** Daha az parametre, daha uzun test periyodu

### 2. Curve Fitting
- **Belirti:** Çok spesifik parametre değerleri
- **Çözüm:** Robust parametre aralıkları

### 3. Look-Ahead Bias
- **Belirti:** Gerçekçi olmayan sonuçlar
- **Çözüm:** Strict historical testing

### 4. Survivorship Bias
- **Belirti:** Sadece başarılı enstrümanlar
- **Çözüm:** Tüm enstrümanları dahil et

## 🔄 Sürekli Optimizasyon

### Haftalık Kontroller
- [ ] Win rate değişimi
- [ ] Drawdown seviyeleri
- [ ] Sinyal frekansı
- [ ] Piyasa koşulları

### Aylık Optimizasyon
- [ ] Parametre fine-tuning
- [ ] Yeni filtre testleri
- [ ] Risk parametreleri
- [ ] Performance review

### Üç Aylık Overhaul
- [ ] Tam parametre optimizasyonu
- [ ] Yeni indikatör testleri
- [ ] Strategy logic review
- [ ] Market regime analysis

---

**Not:** Optimizasyon sürekli bir süreçtir. Piyasa koşulları değiştikçe parametrelerinizi de güncelleyin.
